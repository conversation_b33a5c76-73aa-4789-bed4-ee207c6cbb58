# AI Proxy Configuration Example
# Copy this file to config.toml and update with your actual API keys

[server]
host = "127.0.0.1"
port = 3000

# Provider configurations
# Each provider section defines an AI service provider

[providers.gemini]
api_key = "your-gemini-api-key-here"
api_base = "https://generativelanguage.googleapis.com/v1beta/models/"
models = [
    "gemini-1.5-pro-latest",
    "gemini-1.5-flash-latest",
    "gemini-pro"
]

[providers.openai]
api_key = "your-openai-api-key-here"
api_base = "https://api.openai.com/v1/"
models = [
    "gpt-4",
    "gpt-4-turbo-preview",
    "gpt-3.5-turbo",
    "gpt-3.5-turbo-16k"
]

[providers.anthropic]
api_key = "your-anthropic-api-key-here"
api_base = "https://api.anthropic.com/v1/"
models = [
    "claude-3-opus-20240229",
    "claude-3-sonnet-20240229",
    "claude-3-haiku-20240307"
]

# Environment variable overrides:
# AI_PROXY_SERVER_HOST=0.0.0.0
# AI_PROXY_SERVER_PORT=8080
# AI_PROXY_PROVIDERS_GEMINI_API_KEY=your-key
# AI_PROXY_PROVIDERS_OPENAI_API_KEY=your-key
# AI_PROXY_PROVIDERS_ANTHROPIC_API_KEY=your-key